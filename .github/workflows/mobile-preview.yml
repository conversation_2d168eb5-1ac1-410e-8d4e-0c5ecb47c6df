name: Mobile Preview Build

on:
  pull_request:
    branches:
      - main
    paths:
      - 'mobile/**'
      - '.github/workflows/mobile-preview.yml'
  workflow_dispatch:
    inputs:
      platform:
        description: 'Platform to build'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - ios
          - android

env:
  APP_VARIANT: preview

jobs:
  preview:
    name: Build Preview
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || github.event_name == 'workflow_dispatch'
    defaults:
      run:
        working-directory: ./mobile

    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
          cache-dependency-path: mobile/package-lock.json

      - name: 🏗 Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Run Expo install
        run: npx expo install --fix

      - name: 🔧 Run prebuild
        run: npx expo prebuild --clean --no-install

      - name: 🏗 Build for iOS
        if: ${{ github.event.inputs.platform == 'ios' || github.event.inputs.platform == 'all' || github.event_name == 'pull_request' }}
        run: eas build --platform ios --profile preview --non-interactive --wait

      - name: 🏗 Build for Android
        if: ${{ github.event.inputs.platform == 'android' || github.event.inputs.platform == 'all' || github.event_name == 'pull_request' }}
        run: eas build --platform android --profile preview --non-interactive --wait

      - name: 📱 Publish Preview Update
        run: eas update --branch preview --message "Preview build for PR #${{ github.event.number }}"

      - name: 💬 Comment on PR
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const { data: builds } = await github.rest.repos.listCommitStatusesForRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.sha
            });
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 Preview builds have been created for this PR!
              
              You can download and test the builds from your EAS dashboard.
              
              OTA Update has been published to the \`preview\` channel.`
            })
