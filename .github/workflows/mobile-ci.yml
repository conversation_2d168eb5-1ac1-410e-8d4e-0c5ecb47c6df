name: Mobile CI

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'mobile/**'
      - '.github/workflows/mobile-ci.yml'
  pull_request:
    branches:
      - main
      - develop
    paths:
      - 'mobile/**'
      - '.github/workflows/mobile-ci.yml'

jobs:
  ci:
    name: CI
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./mobile

    steps:
      - name: 🏗 Setup repo
        uses: actions/checkout@v4

      - name: 🏗 Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
          cache-dependency-path: mobile/package-lock.json

      - name: 🏗 Setup EAS
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🚀 Run Expo install
        run: npx expo install --fix

      - name: 🔧 Run prebuild (iOS)
        run: npx expo prebuild --platform ios --clean --no-install
        env:
          APP_VARIANT: development

      - name: 🔧 Run prebuild (Android)
        run: npx expo prebuild --platform android --clean --no-install
        env:
          APP_VARIANT: development

      - name: 🧹 Lint code
        run: npm run lint

      - name: 🔍 Check TypeScript
        run: npx tsc --noEmit
