import { useEffect, useMemo } from "react";
import { View, Text, StyleSheet } from "react-native";
import { CompletionModal } from "../ui/processing/CompletionModal";
import { GenerationStatus } from "../ui/processing/GenerationStatus";
import { TripPreview } from "../ui/processing/TripPreview";
import { useTripGeneration } from "../lib/hooks/useTripGeneration";
import { COLORS } from "../lib/constants/processing-constants";

/**
 * Processing screen
 * Shows the trip generation process
 */
export default function ProcessingScreen() {
  const {
    status,
    isLoading,
    tripId,
    error,
    done,
    tripDetails,
    showDaysQuotaModal,
    setShowDaysQuotaModal,
    requiredDays,
    balance,
    fetchBalance,
    processItinerary,
  } = useTripGeneration();

  const handleRetry = () => {
    console.log("Retrying itinerary generation...");
    processItinerary();
  };

  useEffect(() => {
    processItinerary();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const showCompletionModal = useMemo(() => {
    if (!tripId || error) return false;
    return done;
  }, [tripId, error, done]);

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Crafting Your Perfect Trip</Text>

        <GenerationStatus
          status={status}
          tripId={tripId}
          error={error}
          isLoading={isLoading}
          retry={handleRetry}
        />

        {tripId && <TripPreview tripDetails={tripDetails} tripId={tripId} />}

        <CompletionModal visible={showCompletionModal} tripId={tripId!} />


      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    alignItems: "center",
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: COLORS.PRIMARY,
    marginBottom: 10,
    textAlign: "center",
  },
});
