const IS_DEV = process.env.APP_VARIANT === "development";
const IS_PREVIEW = process.env.APP_VARIANT === "preview";

const getUniqueIdentifier = () => {
  if (IS_DEV) {
    return "com.aiplanmytrip.tripitineraryplanner.dev";
  }

  if (IS_PREVIEW) {
    return "com.aiplanmytrip.tripitineraryplanner.preview";
  }

  return "com.aiplanmytrip.tripitineraryplanner";
};

const getAppName = () => {
  if (IS_DEV) {
    return "AiPlanMyTrip (Dev)";
  }

  if (IS_PREVIEW) {
    return "AiPlanMyTrip (Preview)";
  }

  return "AiPlanMyTrip";
};

export default ({ config }) => ({
  ...config,
  name: getAppName(),
  slug: "aiplanmytrip",
  version: "1.0.0",
  orientation: "portrait",
  icon: "./assets/images/logo.png",
  scheme: getUniqueIdentifier(),
  userInterfaceStyle: "automatic",
  newArchEnabled: true,
  ios: {
    supportsTablet: true,
    bundleIdentifier: getUniqueIdentifier(),
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
    },
  },
  android: {
    adaptiveIcon: {
      foregroundImage: "./assets/images/adaptive-icon.png",
      backgroundColor: "#ffffff",
    },
    package: getUniqueIdentifier(),
    edgeToEdgeEnabled: true,
  },
  web: {
    bundler: "metro",
    output: "server",
    favicon: "./assets/images/favicon.png",
  },
  plugins: [
    [
      "@react-native-google-signin/google-signin",
      {
        iosUrlScheme:
          "com.googleusercontent.apps.994695515433-9ulrkcdr7o8au00b7cchq5o3vrq1bqq3",
      },
    ],
    [
      "@rnmapbox/maps",
      {
        RNMapboxMapsDownloadToken: "*****************************************************************************************",
      },
    ],
    [
      "react-native-iap",
      {
        paymentProvider: "both",
      },
    ],
    [
      "expo-router",
      {
        origin: "https://aiplanmytrip.com/",
      },
    ],
    [
      "expo-splash-screen",
      {
        image: "./assets/images/splash-icon.png",
        imageWidth: 200,
        resizeMode: "contain",
        backgroundColor: "#ffffff",
      },
    ],
    [
      "expo-notifications",
      {
        icon: "./assets/images/icon.png",
        color: "#ffffff",
        sounds: ["./assets/sounds/notification.mp3"],
      },
    ],
  ],
  experiments: {
    typedRoutes: true,
  },
  extra: {
    eas: {
      projectId: "6697f23a-9ec0-4289-9c59-a435d7a43fe7",
    },
  },
  updates: {
    url: "https://u.expo.dev/6697f23a-9ec0-4289-9c59-a435d7a43fe7",
  },
  runtimeVersion: {
    policy: "appVersion",
  },
});
