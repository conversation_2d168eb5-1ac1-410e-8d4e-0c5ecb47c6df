import { useEffect, useState, useCallback } from 'react';
import { Alert, Platform } from 'react-native';
import {
  useIAP,
  getAvailablePurchases
} from 'react-native-iap';
import { API_URL } from '../constants';
import { getIAPErrorMessage, isUserCancellation } from '../utils/iap-errors';
import { CommonService } from '../services/common.service';
import { ProductConfig, useProductsConfig } from './use-products-config';

const commonService = new CommonService();

// Get product IDs for current platform
const getProductIds = (configs: ProductConfig[]): { productIds: string[], subscriptionIds: string[] } => {
  const platform = Platform.OS as 'ios' | 'android';

  const productIds: string[] = [];
  const subscriptionIds: string[] = [];

  configs.forEach(config => {
    if (config.type === 'consumable') {
      const productId = config.productId[platform];
      if (productId) {
        productIds.push(productId);
      }
    } else if (config.type === 'subscription') {
      const subscriptionId = config.productId[platform];
      if (subscriptionId) {
        subscriptionIds.push(subscriptionId);
      }
    }
  });

  return { productIds, subscriptionIds };
};

/**
 * Simplified payment hook that uses react-native-iap's built-in useIAP hook
 * Fetches product IDs from backend and handles purchases/subscriptions
 */
export const usePayments = (onBalanceRefresh?: () => Promise<void>) => {
  const {
    connected,
    products: iapProducts,
    subscriptions: iapSubscriptions,
    getProducts: fetchProducts,
    getSubscriptions: fetchSubscriptions,
    currentPurchase,
    finishTransaction,
    currentPurchaseError,
    requestSubscription,
    requestPurchase,
  } = useIAP();
  const productConfigs = useProductsConfig();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);



  // Load products and subscriptions
  const loadProducts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get product IDs for current platform
      const { productIds, subscriptionIds } = getProductIds(productConfigs);

      console.log("Product IDs: ", productIds);
      console.log("Subscription IDs: ", subscriptionIds);

      // Fetch products and subscriptions using react-native-iap hooks
      if (productIds.length > 0) {
        await fetchProducts({ skus: productIds });
      }
      if (subscriptionIds.length > 0) {
        await fetchSubscriptions({ skus: subscriptionIds });
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error loading products:', error);
      setError('Failed to load products');
      setIsLoading(false);
    }

  }, [
    fetchProducts,
    fetchSubscriptions,
    productConfigs,
  ]);

  console.log("Products: ", iapProducts.length);
  console.log("Subscriptions: ", iapSubscriptions.length);


  // Purchase a consumable product
  const purchaseProduct = async (sku: string): Promise<boolean> => {
    if (!connected) {
      Alert.alert('Connection Error', 'Payment service not connected. Please try again.');
      return false;
    }

    await requestPurchase({ sku });
    return true;
  };

  // Subscribe to a subscription
  const purchaseSubscription = async (sku: string): Promise<boolean> => {
    if (!connected) {
      Alert.alert('Connection Error', 'Payment service not connected. Please try again.');
      return false;
    }

    await requestSubscription({ sku });
    return true;

  };

  // Restore previous purchases
  const restorePurchases = async (): Promise<{ success: boolean; message: string }> => {
    try {
      if (!connected) {
        return { success: false, message: 'Payment service not connected' };
      }

      // Get available purchases from the store
      const purchases = await getAvailablePurchases();

      if (purchases.length === 0) {
        return { success: true, message: 'No previous purchases found to restore' };
      }

      // Validate each purchase with the backend
      const headers = await commonService.setTokenInHeaders();
      const response = await fetch(`${API_URL}/payments/restore`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          purchases: purchases.map((p) => ({
            productId: p.productId,
            transactionId: p.transactionId,
            transactionDate: p.transactionDate,
            receipt: p.transactionReceipt,
          })),
          platform: Platform.OS,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to restore purchases');
      }

      const result = await response.json();
      return {
        success: true,
        message: result.message || `Successfully restored ${purchases.length} purchase(s)`
      };

    } catch (error: any) {
      console.error('Restore purchases failed:', error);

      // Get user-friendly error message for IAP errors
      let errorMessage = 'Failed to restore purchases. Please try again.';
      if (error.code) {
        const iapErrorMessage = getIAPErrorMessage(error.code);
        if (iapErrorMessage) {
          errorMessage = iapErrorMessage;
        }
      }

      return {
        success: false,
        message: errorMessage
      };
    }
  };

  // Handle purchase updates
  useEffect(() => {
    if (currentPurchase && productConfigs.length > 0) {
      const handlePurchase = async () => {
        try {
          const platform = Platform.OS as 'ios' | 'android';
          const config = productConfigs.find(config =>
            config.productId[platform] === currentPurchase.productId
          )

          if (!config) {
            console.error('Unknown product purchased:', currentPurchase.productId);
            Alert.alert(
              'Purchase Error',
              'Unknown product purchased. Please contact support if this issue persists.'
            );
            return;
          }

          // Validate purchase with backend
          const headers = await commonService.setTokenInHeaders();
          const response = await fetch(`${API_URL}/payments/validate`, {
            method: 'POST',
            headers,
            body: JSON.stringify({
              receipt: currentPurchase.transactionReceipt,
              productId: currentPurchase.productId,
              packageId: config.id,
              platform: Platform.OS,
              packageType: config.type === 'consumable' ? 'day_refill' : 'subscription',
              originalTransactionId: currentPurchase.originalTransactionIdentifierIOS,
              transactionId: currentPurchase.transactionId,
              transactionDate: currentPurchase.transactionDate,
              ...(config.type === 'subscription'
                ? { daysPerMonth: config.daysPerMonth }
                : { days: config.days }
              ),
            }),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || 'Purchase validation failed');
          }

          await response.json(); // Consume the response


          // Finish the transaction
          await finishTransaction({
            purchase: currentPurchase,
            isConsumable: config?.type === 'consumable',
          });

          // Refresh balance after successful purchase
          if (onBalanceRefresh) {
            try {
              await onBalanceRefresh();
            } catch (refreshError) {
              console.warn('Failed to refresh balance after purchase:', refreshError);
            }
          }
        } catch (error) {
          console.error('Purchase processing failed:', error);

          // Show user-friendly error message
          const errorMessage = error instanceof Error
            ? error.message
            : 'Purchase processing failed. Please try again.';

          Alert.alert(
            'Purchase Error',
            errorMessage,
            [
              { text: 'OK' },
              {
                text: 'Contact Support',
                onPress: () => {
                  // TODO: Add support contact functionality
                  console.log('Contact support requested');
                }
              }
            ]
          );
        }
      };

      handlePurchase();
    }
  }, [currentPurchase, productConfigs]);

  useEffect(() => {
    if (currentPurchaseError) {
      const message = getIAPErrorMessage(currentPurchaseError.message);
      if (message) {
        Alert.alert('Purchase Error', message);
      }
    }
  }, [currentPurchaseError]);

  // Load products when connected
  useEffect(() => {
    if (connected) {
      loadProducts();
    }
  }, [connected, loadProducts]);

  return {
    // State
    products: iapProducts,
    subscriptions: iapSubscriptions,
    productConfigs,
    isConnected: connected,
    isLoading,
    error,

    // Actions
    purchaseProduct,
    purchaseSubscription,
    restorePurchases,
    retry: loadProducts,
  };
};
