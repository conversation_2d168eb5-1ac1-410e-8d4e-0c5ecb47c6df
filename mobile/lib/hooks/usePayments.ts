import { useEffect, useState } from 'react';
import { Alert, Platform } from 'react-native';
import {
  requestPurchase,
  requestSubscription,
  useIAP
} from 'react-native-iap';
import { API_URL } from '../constants';
import { CommonService } from '../services/common.service';

export interface ProductConfig {
  id: string;
  name: string;
  description: string;
  type: 'subscription' | 'consumable';
  platform: 'ios' | 'android' | 'both';
  productId: {
    ios?: string;
    android?: string;
  };
  daysPerMonth?: number;
  days?: number;
  price: {
    usd: number;
    currency: string;
  };
  popular?: boolean;
}

const commonService = new CommonService();

/**
 * Simplified payment hook that uses react-native-iap's built-in useIAP hook
 * Fetches product IDs from backend and handles purchases/subscriptions
 */
export const usePayments = () => {
  const {
    connected,
    products: iapProducts,
    subscriptions: iapSubscriptions,
    getProducts: fetchProducts,
    getSubscriptions: fetchSubscriptions,
    currentPurchase,
    finishTransaction,
  } = useIAP();

  const [productConfigs, setProductConfigs] = useState<ProductConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch product configurations from backend
  const fetchProductConfigs = async (): Promise<ProductConfig[]> => {
    try {
      const headers = await commonService.setTokenInHeaders();
      const platform = Platform.OS as 'ios' | 'android';

      const response = await fetch(`${API_URL}/payments/products?platform=${platform}`, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new Error('Failed to fetch product configs');
      }

      const data = await response.json();
      return data.products || [];
    } catch (error) {
      console.error('Error fetching product configs:', error);
      throw error;
    }
  };

  // Get product IDs for current platform
  const getProductIds = (configs: ProductConfig[]): { productIds: string[], subscriptionIds: string[] } => {
    const platform = Platform.OS as 'ios' | 'android';

    const productIds: string[] = [];
    const subscriptionIds: string[] = [];

    configs.forEach(config => {
      const storeId = config.productId[platform];
      if (storeId && (config.platform === 'both' || config.platform === platform)) {
        if (config.type === 'consumable') {
          productIds.push(storeId);
        } else if (config.type === 'subscription') {
          subscriptionIds.push(storeId);
        }
      }
    });

    return { productIds, subscriptionIds };
  };

  // Load products and subscriptions
  const loadProducts = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch product configurations from backend
      const configs = await fetchProductConfigs();
      setProductConfigs(configs);

      // Get product IDs for current platform
      const { productIds, subscriptionIds } = getProductIds(configs);

      // Fetch products and subscriptions using react-native-iap hooks
      if (productIds.length > 0) {
        await fetchProducts({ skus: productIds });
      }
      if (subscriptionIds.length > 0) {
        await fetchSubscriptions({ skus: subscriptionIds });
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error loading products:', error);
      setError('Failed to load products');
      setIsLoading(false);
    }
  };

  // Purchase a consumable product
  const purchaseProduct = async (sku: string): Promise<boolean> => {
    try {
      await requestPurchase({ sku, });
      return true;
    } catch (error) {
      console.error('Purchase failed:', error);
      Alert.alert('Purchase Failed', 'Unable to complete purchase. Please try again.');
      return false;
    }
  };

  // Subscribe to a subscription
  const purchaseSubscription = async (sku: string): Promise<boolean> => {
    try {
      await requestSubscription({
        sku,
      });

      return true;
    } catch (error) {
      console.error('Subscription failed:', error);
      Alert.alert('Subscription Failed', 'Unable to complete subscription. Please try again.');
      return false;
    }
  };

  // Get product config by store product ID
  const getProductConfig = (storeProductId: string): ProductConfig | null => {
    const platform = Platform.OS as 'ios' | 'android';
    return productConfigs.find(config =>
      config.productId[platform] === storeProductId
    ) || null;
  };

  // Handle purchase updates
  useEffect(() => {
    if (currentPurchase) {
      const handlePurchase = async () => {
        try {
          const config = getProductConfig(currentPurchase.productId);

          if (config) {
            // Validate purchase with backend
            const headers = await commonService.setTokenInHeaders();
            await fetch(`${API_URL}/payments/validate`, {
              method: 'POST',
              headers,
              body: JSON.stringify({
                receipt: currentPurchase.transactionReceipt,
                productId: currentPurchase.productId,
                packageId: config.id,
                platform: Platform.OS,
                packageType: config.type === 'consumable' ? 'day_refill' : 'subscription',
                originalTransactionId: currentPurchase.originalTransactionIdentifierIOS,
                transactionId: currentPurchase.transactionId,
                transactionDate: currentPurchase.transactionDate,
                ...(config.type === 'subscription'
                  ? { daysPerMonth: config.daysPerMonth }
                  : { days: config.days }
                ),
              }),
            });
          }

          // Finish the transaction
          await finishTransaction({
            purchase: currentPurchase,
            isConsumable: config?.type === 'consumable',
          });

        } catch (error) {
          console.error('Purchase processing failed:', error);
        }
      };

      handlePurchase();
    }
  }, [currentPurchase]);

  // Load products when connected
  useEffect(() => {
    if (connected) {
      loadProducts();
    }
  }, [connected]);

  return {
    // State
    products: iapProducts,
    subscriptions: iapSubscriptions,
    productConfigs,
    isConnected: connected,
    isLoading,
    error,

    // Actions
    purchaseProduct,
    purchaseSubscription,
    getProductConfig,
    retry: loadProducts,
  };
};
