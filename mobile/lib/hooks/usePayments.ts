import { useEffect, useState, useCallback } from 'react';
import { Alert, Platform } from 'react-native';
import {
  requestPurchase,
  requestSubscription,
  useIAP,
  getAvailablePurchases
} from 'react-native-iap';
import { API_URL } from '../constants';
import { CommonService } from '../services/common.service';

export interface ProductConfig {
  id: string;
  name: string;
  description: string;
  type: 'subscription' | 'consumable';
  platform: 'ios' | 'android' | 'both';
  productId: {
    ios?: string;
    android?: string;
  };
  daysPerMonth?: number;
  days?: number;
  price: {
    usd: number;
    currency: string;
  };
  popular?: boolean;
}

const commonService = new CommonService();

/**
 * Simplified payment hook that uses react-native-iap's built-in useIAP hook
 * Fetches product IDs from backend and handles purchases/subscriptions
 */
export const usePayments = (onBalanceRefresh?: () => Promise<void>) => {
  const {
    connected,
    products: iapProducts,
    subscriptions: iapSubscriptions,
    getProducts: fetchProducts,
    getSubscriptions: fetchSubscriptions,
    currentPurchase,
    finishTransaction,
  } = useIAP();

  const [productConfigs, setProductConfigs] = useState<ProductConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch product configurations from backend
  const fetchProductConfigs = async (): Promise<ProductConfig[]> => {
    try {
      const headers = await commonService.setTokenInHeaders();
      const platform = Platform.OS as 'ios' | 'android';

      const response = await fetch(`${API_URL}/payments/products?platform=${platform}`, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        throw new Error('Failed to fetch product configs');
      }

      const data = await response.json();
      return data.products || [];
    } catch (error) {
      console.error('Error fetching product configs:', error);
      throw error;
    }
  };

  // Get product IDs for current platform
  const getProductIds = (configs: ProductConfig[]): { productIds: string[], subscriptionIds: string[] } => {
    const platform = Platform.OS as 'ios' | 'android';

    const productIds: string[] = [];
    const subscriptionIds: string[] = [];

    configs.forEach(config => {
      const storeId = config.productId[platform];
      if (storeId && (config.platform === 'both' || config.platform === platform)) {
        if (config.type === 'consumable') {
          productIds.push(storeId);
        } else if (config.type === 'subscription') {
          subscriptionIds.push(storeId);
        }
      }
    });

    return { productIds, subscriptionIds };
  };

  // Load products and subscriptions
  const loadProducts = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch product configurations from backend
      const configs = await fetchProductConfigs();
      setProductConfigs(configs);

      // Get product IDs for current platform
      const { productIds, subscriptionIds } = getProductIds(configs);

      // Fetch products and subscriptions using react-native-iap hooks
      if (productIds.length > 0) {
        await fetchProducts({ skus: productIds });
      }
      if (subscriptionIds.length > 0) {
        await fetchSubscriptions({ skus: subscriptionIds });
      }

      setIsLoading(false);
    } catch (error) {
      console.error('Error loading products:', error);
      setError('Failed to load products');
      setIsLoading(false);
    }
  };

  // Purchase a consumable product
  const purchaseProduct = async (sku: string): Promise<boolean> => {
    try {
      if (!connected) {
        Alert.alert('Connection Error', 'Payment service not connected. Please try again.');
        return false;
      }

      await requestPurchase({ sku });
      return true;
    } catch (error) {
      console.error('Purchase failed:', error);

      // Handle specific error types
      let errorMessage = 'Unable to complete purchase. Please try again.';

      if (error instanceof Error) {
        if (error.message.includes('cancelled') || error.message.includes('canceled')) {
          // User cancelled - don't show error
          return false;
        } else if (error.message.includes('network')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.message.includes('billing')) {
          errorMessage = 'Billing service unavailable. Please try again later.';
        }
      }

      Alert.alert('Purchase Failed', errorMessage);
      return false;
    }
  };

  // Subscribe to a subscription
  const purchaseSubscription = async (sku: string): Promise<boolean> => {
    try {
      if (!connected) {
        Alert.alert('Connection Error', 'Payment service not connected. Please try again.');
        return false;
      }

      await requestSubscription({ sku });
      return true;
    } catch (error) {
      console.error('Subscription failed:', error);

      // Handle specific error types
      let errorMessage = 'Unable to complete subscription. Please try again.';

      if (error instanceof Error) {
        if (error.message.includes('cancelled') || error.message.includes('canceled')) {
          // User cancelled - don't show error
          return false;
        } else if (error.message.includes('network')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (error.message.includes('billing')) {
          errorMessage = 'Billing service unavailable. Please try again later.';
        }
      }

      Alert.alert('Subscription Failed', errorMessage);
      return false;
    }
  };

  // Get product config by store product ID
  const getProductConfig = useCallback((storeProductId: string): ProductConfig | null => {
    const platform = Platform.OS as 'ios' | 'android';

    console.log("searching for product configs in pool of  length = ", productConfigs.length);


    return productConfigs.find(config =>
      config.productId[platform] === storeProductId
    ) || null;
  }, [productConfigs]);

  // Restore previous purchases
  const restorePurchases = async (): Promise<{ success: boolean; message: string }> => {
    try {
      if (!connected) {
        return { success: false, message: 'Payment service not connected' };
      }

      // Get available purchases from the store
      const purchases = await getAvailablePurchases();

      if (purchases.length === 0) {
        return { success: true, message: 'No previous purchases found to restore' };
      }

      // Validate each purchase with the backend
      const headers = await commonService.setTokenInHeaders();
      const response = await fetch(`${API_URL}/payments/restore`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          purchases: purchases.map((p) => ({
            productId: p.productId,
            transactionId: p.transactionId,
            transactionDate: p.transactionDate,
            receipt: p.transactionReceipt,
          })),
          platform: Platform.OS,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to restore purchases');
      }

      const result = await response.json();
      return {
        success: true,
        message: result.message || `Successfully restored ${purchases.length} purchase(s)`
      };

    } catch (error) {
      console.error('Restore purchases failed:', error);
      return {
        success: false,
        message: 'Failed to restore purchases. Please try again.'
      };
    }
  };

  // Handle purchase updates
  useEffect(() => {
    if (currentPurchase && productConfigs.length > 0) {
      const handlePurchase = async () => {
        try {
          const platform = Platform.OS as 'ios' | 'android';
          const config = productConfigs.find(config =>
            config.productId[platform] === currentPurchase.productId
          )

          if (!config) {
            console.error('Unknown product purchased:', currentPurchase.productId);
            Alert.alert(
              'Purchase Error',
              'Unknown product purchased. Please contact support if this issue persists.'
            );
            return;
          }

          // Validate purchase with backend
          const headers = await commonService.setTokenInHeaders();
          const response = await fetch(`${API_URL}/payments/validate`, {
            method: 'POST',
            headers,
            body: JSON.stringify({
              receipt: currentPurchase.transactionReceipt,
              productId: currentPurchase.productId,
              packageId: config.id,
              platform: Platform.OS,
              packageType: config.type === 'consumable' ? 'day_refill' : 'subscription',
              originalTransactionId: currentPurchase.originalTransactionIdentifierIOS,
              transactionId: currentPurchase.transactionId,
              transactionDate: currentPurchase.transactionDate,
              ...(config.type === 'subscription'
                ? { daysPerMonth: config.daysPerMonth }
                : { days: config.days }
              ),
            }),
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.message || 'Purchase validation failed');
          }

          await response.json(); // Consume the response


          // Finish the transaction
          await finishTransaction({
            purchase: currentPurchase,
            isConsumable: config?.type === 'consumable',
          });

          // Refresh balance after successful purchase
          if (onBalanceRefresh) {
            try {
              await onBalanceRefresh();
            } catch (refreshError) {
              console.warn('Failed to refresh balance after purchase:', refreshError);
            }
          }
        } catch (error) {
          console.error('Purchase processing failed:', error);

          // Show user-friendly error message
          const errorMessage = error instanceof Error
            ? error.message
            : 'Purchase processing failed. Please try again.';

          Alert.alert(
            'Purchase Error',
            errorMessage,
            [
              { text: 'OK' },
              {
                text: 'Contact Support',
                onPress: () => {
                  // TODO: Add support contact functionality
                  console.log('Contact support requested');
                }
              }
            ]
          );
        }
      };

      handlePurchase();
    }
  }, [currentPurchase, productConfigs]);

  // Load products when connected
  useEffect(() => {
    if (connected) {
      loadProducts();
    }
  }, [connected]);

  return {
    // State
    products: iapProducts,
    subscriptions: iapSubscriptions,
    productConfigs,
    isConnected: connected,
    isLoading,
    error,

    // Actions
    purchaseProduct,
    purchaseSubscription,
    getProductConfig,
    restorePurchases,
    retry: loadProducts,
  };
};
