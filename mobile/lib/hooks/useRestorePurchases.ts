import { useState } from "react";
import { Alert } from "react-native";
import { router } from "expo-router";
// TODO: Update to use usePayments hook for restore functionality
import { mediumHapticFeedback } from "../utils/haptics";

interface UseRestorePurchasesProps {
  paymentService: PaymentService;
  fetchBalance: () => Promise<void>;
}

/**
 * Hook for handling restore purchases functionality
 * @param paymentService Payment service instance
 * @param fetchBalance Function to fetch days balance
 * @returns Restore purchases state and function
 */
export const useRestorePurchases = ({
  paymentService,
  fetchBalance,
}: UseRestorePurchasesProps) => {
  const [isRestoring, setIsRestoring] = useState<boolean>(false);

  const handleRestorePurchases = async () => {
    try {
      setIsRestoring(true);
      mediumHapticFeedback();

      const result = await paymentService.restorePurchases();

      if (result.success) {
        // Refresh balance after restore
        await fetchBalance();

        Alert.alert("Restore Complete", result.message, [
          {
            text: "View My Days",
            onPress: () => router.push("/credits-dashboard"),
          },
          {
            text: "OK",
            style: "cancel",
          },
        ]);
      } else {
        Alert.alert("Restore Failed", result.message);
      }
    } catch (error) {
      Alert.alert(
        "Restore Failed",
        "There was an error restoring your purchases. Please try again.",
      );
    } finally {
      setIsRestoring(false);
    }
  };

  return {
    isRestoring,
    handleRestorePurchases,
  };
};
