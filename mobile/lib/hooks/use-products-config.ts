import React from "react";
import { API_URL } from "../constants";
import { CommonService } from '../services/common.service';

const commonService = new CommonService();
export interface ProductConfig {
    id: string;
    name: string;
    description: string;
    type: 'subscription' | 'consumable';
    platform: 'ios' | 'android' | 'both';
    productId: {
        ios?: string;
        android?: string;
    };
    daysPerMonth?: number;
    days?: number;
    price: {
        usd: number;
        currency: string;
    };
    popular?: boolean;
}



export const useProductsConfig = () => {
    const [products, setProducts] = React.useState<ProductConfig[]>([]);

    const loadProducts = async () => {
        try {
            const headers = await commonService.setTokenInHeaders();
            const response = await fetch(`${API_URL}/payments/products`, {
                method: 'GET',
                headers,
            });
            if (!response.ok) {
                throw new Error('Failed to fetch products');
            }
            const data = await response.json();
            setProducts(data.products);
        } catch (error) {
            console.error('Error fetching products:', error);
        }
    };

    React.useEffect(() => {
        loadProducts();
    }, []);

    return products;
};
