import { useState, useEffect } from "react";
import { Alert } from "react-native";
import { Product } from "react-native-iap";
import { PaymentService, SubscriptionPlan, DayRefillPackage } from "../services/payment.service";


/**
 * Hook for initializing payment service and loading products
 * @returns Payment initialization state and products
 */
export const usePaymentInitialization = () => {
  const [products, setProducts] = useState<(Product & (SubscriptionPlan | DayRefillPackage))[]>([]);
  const [isInitializing, setIsInitializing] = useState<boolean>(true);
  const paymentService = PaymentService.getInstance();

  useEffect(() => {
    const initPayments = async () => {
      try {
        setIsInitializing(true);

        // Try to initialize the payment service
        const initSuccess = await paymentService.initialize();

        // Get both subscription and day refill products
        const [subscriptionProducts, dayRefillProducts] = await Promise.all([
          paymentService.getSubscriptionProducts(),
          paymentService.getDayRefillProducts(),
        ]);

        // Combine both types of products
        const allProducts = [...subscriptionProducts, ...dayRefillProducts];
        setProducts(allProducts);

        // If initialization failed, log the warning
        if (!initSuccess) {
          console.warn("Payment initialization failed");
        }
      } catch (error) {
        console.error("Failed to initialize payments:", error);
        Alert.alert(
          "Payment Setup Failed",
          "We couldn't set up the payment system. Please try again later.",
        );
      } finally {
        setIsInitializing(false);
      }
    };

    initPayments();

    // Clean up when component unmounts
    return () => {
      paymentService.cleanup();
    };
  }, []);

  return {
    products,
    isInitializing,
    paymentService,
  };
};
