import { router } from "expo-router";
import { useState } from "react";
import { Alert } from "react-native";
import { type Product } from "react-native-iap";
import {
  DayRefillPackage,
  dayRefillPackages,
  PaymentService,
  SubscriptionPlan,
  subscriptionPlans,
} from "../services/payment.service";
import { mediumHapticFeedback } from "../utils/haptics";

interface UsePurchaseHandlerProps {
  paymentService: PaymentService;
  products: Product & (SubscriptionPlan | DayRefillPackage)[];
  fetchBalance: () => Promise<void>;
}

/**
 * Hook for handling credit package purchases
 * @param paymentService Payment service instance
 * @param products Available products
 * @param fetchBalance Function to fetch token balance
 * @returns Purchase handler state and functions
 */
export const usePurchaseHandler = ({
  paymentService,
  products,
  fetchBalance,
}: UsePurchaseHandlerProps) => {
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [selectedDayRefillInfo, setSelectedDayRefillInfo] = useState<{
    id: string;
    days: number;
    price: number;
  } | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleSelectPackage = (packageId: string, days?: number, price?: number) => {
    setSelectedPackage(packageId);

    // If this is a day refill package (has days and price), store the info
    if (days !== undefined && price !== undefined) {
      setSelectedDayRefillInfo({ id: packageId, days, price });
    } else {
      setSelectedDayRefillInfo(null);
    }

    mediumHapticFeedback(); // Provide haptic feedback
  };

  const handlePurchase = async () => {
    if (!selectedPackage) {
      Alert.alert("Error", "Please select a plan or package");
      return;
    }

    // Find the selected item in either subscription plans or day refill packages
    const subscriptionPlan = subscriptionPlans.find((p) => p.id === selectedPackage);
    const dayRefillPackage = dayRefillPackages.find((p) => p.id === selectedPackage);

    // For backend day refill packages, use the stored info
    const backendDayRefill = selectedDayRefillInfo;

    const selectedItem = subscriptionPlan || dayRefillPackage || backendDayRefill;

    if (!selectedItem) return;

    // For subscription plans, find the corresponding product
    // For day refill packages, we'll handle them differently
    let product = null;
    let productId = "";

    if (subscriptionPlan) {
      product = products.find((p) => p.id === selectedPackage);
      if (!product) {
        Alert.alert("Error", "Selected subscription is not available for purchase");
        return;
      }
      productId = subscriptionPlan.productId;
    } else if (dayRefillPackage) {
      product = products.find((p) => p.id === selectedPackage);
      if (!product) {
        Alert.alert("Error", "Selected day refill package is not available for purchase");
        return;
      }
      productId = dayRefillPackage.productId;
    } else if (backendDayRefill) {
      // For backend day refill, we need to find the closest matching product
      // or use a base product ID pattern
      const baseDays = Math.ceil(backendDayRefill.days / 5) * 5; // Round up to nearest 5
      productId = `com.aiplanmytrip.days.refill_${baseDays}`;
    }

    // Create confirmation message based on item type
    let confirmationMessage = "";
    if (subscriptionPlan) {
      confirmationMessage = `Are you sure you want to subscribe to the ${subscriptionPlan.name} plan (${subscriptionPlan.daysPerMonth} days/month) for ${(product as any)?.localizedPrice || `$${subscriptionPlan.price}/month`}?`;
    } else if (dayRefillPackage) {
      confirmationMessage = `Are you sure you want to purchase ${dayRefillPackage.days} additional days for ${(product as any)?.localizedPrice || `$${dayRefillPackage.price}`}?`;
    } else if (backendDayRefill) {
      confirmationMessage = `Are you sure you want to purchase ${backendDayRefill.days} additional days for $${backendDayRefill.price.toFixed(2)}?`;
    }

    // Confirm purchase with actual price from store
    Alert.alert(
      "Confirm Purchase",
      confirmationMessage,
      [
        {
          text: "Cancel",
          style: "cancel",
        },
        {
          text: subscriptionPlan ? "Subscribe" : "Purchase",
          onPress: async () => {
            try {
              setIsLoading(true);

              // Trigger haptic feedback
              mediumHapticFeedback();

              // Request the purchase
              const success = await paymentService.purchaseProduct(
                productId,
              );

              if (success) {
                // Refresh balance after purchase
                await fetchBalance();

                const successMessage = subscriptionPlan
                  ? "Your subscription is being processed. Days will be added to your account shortly."
                  : "Your purchase is being processed. Days will be added to your account shortly.";

                Alert.alert(
                  "Purchase Processing",
                  successMessage,
                  [
                    {
                      text: "Done",
                      onPress: () => router.dismiss(),
                    },
                  ],
                );
              }
            } catch (error) {
              console.error("Purchase error:", error);
              Alert.alert(
                "Purchase Failed",
                "There was an error processing your purchase. Please try again.",
              );
            } finally {
              setIsLoading(false);
            }
          },
        },
      ],
    );
  };

  return {
    selectedPackage,
    isLoading,
    handleSelectPackage,
    handlePurchase,
  };
};
