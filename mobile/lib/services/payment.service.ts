import { Platform } from "react-native";
import {
  initConnection,
  endConnection,
  getProducts,
  requestPurchase,
  finishTransaction,
  purchaseUpdatedListener,
  purchaseErrorListener,
  getAvailablePurchases,
  Product,
  Purchase,
  PurchaseError,
} from "react-native-iap";
import { API_URL } from "../constants";
import { CommonService } from "./common.service";


export interface SubscriptionPlan {
  id: string;
  productId: string;
  name: string;
  daysPerMonth: number;
  price: number;
  popular?: boolean;
  type: "subscription";
}

export interface DayRefillPackage {
  id: string;
  productId: string;
  name: string;
  days: number;
  price: number;
  type: "day_refill";
}

// Define product IDs for in-app purchases
export const productIds = Platform.select({
  ios: [
    "com.itrip.subscription.free",
    "com.itrip.subscription.pro",
    "com.itrip.subscription.premium",
    "com.itrip.days.refill_10",
    "com.itrip.days.refill_25",
    "com.itrip.days.refill_50",
  ],
  android: [
    "com.itrip.subscription.free",
    "com.itrip.subscription.pro",
    "com.itrip.subscription.premium",
    "com.itrip.days.refill_10",
    "com.itrip.days.refill_25",
    "com.itrip.days.refill_50",
  ],
  default: [],
});

// Subscription plans configuration
export const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: "free",
    productId: "com.itrip.subscription.free",
    name: "Free",
    daysPerMonth: 5,
    price: 0,
    type: "subscription",
  },
  {
    id: "pro",
    productId: "com.itrip.subscription.pro",
    name: "Pro",
    daysPerMonth: 50,
    price: 9.99,
    popular: true,
    type: "subscription",
  },
  {
    id: "premium",
    productId: "com.itrip.subscription.premium",
    name: "Premium",
    daysPerMonth: 100,
    price: 19.99,
    type: "subscription",
  },
];

// Day refill packages configuration
export const dayRefillPackages: DayRefillPackage[] = [
  {
    id: "refill_10",
    productId: "com.itrip.days.refill_10",
    name: "10 Days",
    days: 10,
    price: 1.99,
    type: "day_refill",
  },
  {
    id: "refill_25",
    productId: "com.itrip.days.refill_25",
    name: "25 Days",
    days: 25,
    price: 4.99,
    type: "day_refill",
  },
  {
    id: "refill_50",
    productId: "com.itrip.days.refill_50",
    name: "50 Days",
    days: 50,
    price: 8.99,
    type: "day_refill",
  },
];

export class PaymentService extends CommonService {
  private static instance: PaymentService;
  private products: Product[] = [];
  private purchaseUpdateSubscription: any = null;
  private purchaseErrorSubscription: any = null;
  private isConnected: boolean = false;

  private constructor() {
    super();
  }

  static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }

  /**
   * Initialize the payment service
   * This should be called when the app starts
   */
  async initialize(): Promise<boolean> {
    try {
      if (this.isConnected) {
        return true;
      }

      // Initialize connection to the store
      await initConnection();
      this.isConnected = true;

      // Set up listeners for purchase updates and errors
      this.purchaseUpdateSubscription = purchaseUpdatedListener(
        async (purchase: Purchase) => {
          // Process the purchase
          await this.processPurchase(purchase);
        },
      );

      this.purchaseErrorSubscription = purchaseErrorListener(
        (error: PurchaseError) => {
          console.error("Purchase error:", error);
        },
      );

      // Load available products
      await this.loadProducts();

      return true;
    } catch (error) {
      console.error("Failed to initialize payment service:", error);
      return false;
    }
  }

  /**
   * Clean up the payment service
   * This should be called when the app is closed
   */
  async cleanup(): Promise<void> {
    if (this.purchaseUpdateSubscription) {
      this.purchaseUpdateSubscription.remove();
      this.purchaseUpdateSubscription = null;
    }

    if (this.purchaseErrorSubscription) {
      this.purchaseErrorSubscription.remove();
      this.purchaseErrorSubscription = null;
    }

    if (this.isConnected) {
      await endConnection();
      this.isConnected = false;
    }
  }

  /**
   * Load available products from the store
   */
  async loadProducts(): Promise<Product[]> {
    try {
      if (!this.isConnected) {
        await this.initialize();
      }

      this.products = await getProducts({ skus: productIds });
      return this.products;
    } catch (error) {
      console.error("Failed to load products:", error);
      return [];
    }
  }

  /**
   * Get available subscription products
   */
  async getSubscriptionProducts(): Promise<(Product & SubscriptionPlan)[]> {
    if (this.products.length === 0) {
      await this.loadProducts();
    }

    return this.products
      .filter((product) =>
        subscriptionPlans.some((plan) => plan.productId === product.productId)
      )
      .map((product) => {
        const planInfo = subscriptionPlans.find(
          (plan) => plan.productId === product.productId,
        );
        return {
          ...product,
          ...planInfo,
        } as Product & SubscriptionPlan;
      });
  }

  /**
   * Get available day refill products
   */
  async getDayRefillProducts(): Promise<(Product & DayRefillPackage)[]> {
    if (this.products.length === 0) {
      await this.loadProducts();
    }

    return this.products
      .filter((product) =>
        dayRefillPackages.some((pkg) => pkg.productId === product.productId)
      )
      .map((product) => {
        const packageInfo = dayRefillPackages.find(
          (pkg) => pkg.productId === product.productId,
        );
        return {
          ...product,
          ...packageInfo,
        } as Product & DayRefillPackage;
      });
  }



  /**
   * Purchase a product
   * @param productId Product ID to purchase
   */
  async purchaseProduct(productId: string): Promise<boolean> {
    try {
      if (!this.isConnected) {
        await this.initialize();
      }

      // Request the purchase
      await requestPurchase({ skus: [productId] });

      // The purchase will be processed in the purchaseUpdatedListener
      return true;
    } catch (error) {
      console.error("Purchase failed:", error);
      return false;
    }
  }



  /**
   * Process a purchase
   * @param purchase Purchase object from the store
   */
  private async processPurchase(purchase: Purchase): Promise<void> {
    try {
      // Find the corresponding subscription plan or day refill package
      const subscriptionPlan = subscriptionPlans.find(
        (plan) => plan.productId === purchase.productId,
      );
      const dayRefillPackage = dayRefillPackages.find(
        (pkg) => pkg.productId === purchase.productId,
      );

      const packageInfo = subscriptionPlan || dayRefillPackage;

      if (!packageInfo) {
        console.error("Unknown product purchased:", purchase.productId);
        return;
      }

      // Validate the purchase with the backend
      const validationResult = await this.validatePurchase(
        purchase,
        packageInfo,
      );

      if (validationResult.success) {
        // Finish the transaction
        await finishTransaction({ purchase });
      }
    } catch (error) {
      console.error("Failed to process purchase:", error);
    }
  }



  /**
   * Validate a purchase with the backend
   * @param purchase Purchase object from the store
   * @param packageInfo Subscription plan or day refill package information
   */
  private async validatePurchase(
    purchase: Purchase,
    packageInfo: SubscriptionPlan | DayRefillPackage,
  ): Promise<{ success: boolean; message?: string }> {
    try {
      const headers = await this.setTokenInHeaders();

      // Prepare payload based on package type
      const payload: any = {
        receipt: purchase.transactionReceipt,
        productId: purchase.productId,
        platform: Platform.OS,
        packageId: packageInfo.id,
        packageType: packageInfo.type,
      };

      // Add type-specific fields
      if (packageInfo.type === "subscription") {
        payload.daysPerMonth = (packageInfo as SubscriptionPlan).daysPerMonth;
      } else if (packageInfo.type === "day_refill") {
        payload.days = (packageInfo as DayRefillPackage).days;
      }

      const response = await fetch(`${API_URL}/payments/validate`, {
        method: "POST",
        headers,
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error("Failed to validate purchase");
      }

      return await response.json();
    } catch (error) {
      console.error("Purchase validation failed:", error);
      return { success: false, message: "Purchase validation failed" };
    }
  }

  /**
   * Restore previous purchases
   */
  async restorePurchases(): Promise<{ success: boolean; message: string }> {
    try {


      if (!this.isConnected) {
        await this.initialize();
      }

      // Get available purchases
      const purchases = await getAvailablePurchases();

      if (purchases.length === 0) {
        return { success: true, message: "No previous purchases found" };
      }

      // Validate each purchase with the backend
      const headers = await this.setTokenInHeaders();
      const response = await fetch(`${API_URL}/payments/restore`, {
        method: "POST",
        headers,
        body: JSON.stringify({
          purchases: purchases.map((p) => ({
            productId: p.productId,
            transactionId: p.transactionId,
            transactionDate: p.transactionDate,
            receipt: p.transactionReceipt,
          })),
          platform: Platform.OS,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to restore purchases");
      }

      const result = await response.json();
      return {
        success: true,
        message: `Restored ${result.restoredCount} purchases`,
      };
    } catch (error) {
      console.error("Failed to restore purchases:", error);
      return { success: false, message: "Failed to restore purchases" };
    }
  }


}
