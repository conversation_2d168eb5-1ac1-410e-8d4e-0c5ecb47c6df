import React, { useState } from "react";
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { subscriptionPlans } from "../../lib/services/payment.service";
import { CommonService } from "../../lib/services/common.service";
import { API_URL } from "../../lib/constants";
import { mediumHapticFeedback } from "../../lib/utils/haptics";

interface PlanChangeModalProps {
  visible: boolean;
  onClose: () => void;
  currentPlan: string;
  onPlanChanged: () => void;
}

export const PlanChangeModal: React.FC<PlanChangeModalProps> = ({
  visible,
  onClose,
  currentPlan,
  onPlanChanged,
}) => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [prorationMode, setProrationMode] = useState<'immediate' | 'next_billing_cycle'>('immediate');

  const handlePlanSelect = (planId: string) => {
    if (planId === currentPlan) return;
    setSelectedPlan(planId);
    mediumHapticFeedback();
  };

  const handleChangePlan = async () => {
    if (!selectedPlan) return;

    try {
      setIsLoading(true);
      const commonService = new CommonService();
      const headers = await commonService.setTokenInHeaders();

      const response = await fetch(`${API_URL}/payments/subscription/change`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          newPlan: selectedPlan,
          prorationMode,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        Alert.alert(
          "Plan Changed",
          data.message,
          [
            {
              text: "OK",
              onPress: () => {
                onPlanChanged();
                onClose();
              },
            },
          ]
        );
      } else {
        Alert.alert("Error", data.message || "Failed to change plan");
      }
    } catch (error) {
      Alert.alert("Error", "Failed to change plan. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const getPlanColor = (planId: string) => {
    switch (planId) {
      case 'free': return '#4CAF50';
      case 'pro': return '#2196F3';
      case 'premium': return '#9C27B0';
      default: return '#666';
    }
  };

  const isUpgrade = (planId: string) => {
    const planOrder = { free: 0, pro: 1, premium: 2 };
    return planOrder[planId as keyof typeof planOrder] > planOrder[currentPlan as keyof typeof planOrder];
  };

  const isDowngrade = (planId: string) => {
    const planOrder = { free: 0, pro: 1, premium: 2 };
    return planOrder[planId as keyof typeof planOrder] < planOrder[currentPlan as keyof typeof planOrder];
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.title}>Change Plan</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Current Plan Info */}
          <View style={styles.currentPlanSection}>
            <Text style={styles.sectionTitle}>Current Plan</Text>
            <View style={styles.currentPlanCard}>
              <View style={[styles.planBadge, { backgroundColor: getPlanColor(currentPlan) }]}>
                <Ionicons name="star" size={16} color="#FFF" />
              </View>
              <View style={styles.currentPlanInfo}>
                <Text style={styles.currentPlanName}>
                  {subscriptionPlans.find(p => p.id === currentPlan)?.name || 'Unknown'}
                </Text>
                <Text style={styles.currentPlanPrice}>
                  ${subscriptionPlans.find(p => p.id === currentPlan)?.price || 0}/month
                </Text>
              </View>
            </View>
          </View>

          {/* Available Plans */}
          <View style={styles.plansSection}>
            <Text style={styles.sectionTitle}>Available Plans</Text>
            {subscriptionPlans
              .filter(plan => plan.id !== currentPlan)
              .map((plan) => (
                <TouchableOpacity
                  key={plan.id}
                  style={[
                    styles.planCard,
                    selectedPlan === plan.id && styles.selectedPlanCard,
                  ]}
                  onPress={() => handlePlanSelect(plan.id)}
                >
                  <View style={styles.planHeader}>
                    <View style={[styles.planBadge, { backgroundColor: getPlanColor(plan.id) }]}>
                      <Ionicons name="star" size={16} color="#FFF" />
                    </View>
                    <View style={styles.planInfo}>
                      <Text style={styles.planName}>{plan.name}</Text>
                      <Text style={styles.planPrice}>${plan.price}/month</Text>
                    </View>
                    {isUpgrade(plan.id) && (
                      <View style={styles.upgradeTag}>
                        <Text style={styles.upgradeText}>Upgrade</Text>
                      </View>
                    )}
                    {isDowngrade(plan.id) && (
                      <View style={styles.downgradeTag}>
                        <Text style={styles.downgradeText}>Downgrade</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.planDescription}>
                    {plan.daysPerMonth} days per month
                  </Text>
                  {selectedPlan === plan.id && (
                    <View style={styles.selectedIndicator}>
                      <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
                    </View>
                  )}
                </TouchableOpacity>
              ))}
          </View>

          {/* Proration Options */}
          {selectedPlan && (
            <View style={styles.prorationSection}>
              <Text style={styles.sectionTitle}>When to apply changes?</Text>
              
              <TouchableOpacity
                style={[
                  styles.prorationOption,
                  prorationMode === 'immediate' && styles.selectedProrationOption,
                ]}
                onPress={() => setProrationMode('immediate')}
              >
                <View style={styles.prorationContent}>
                  <Text style={styles.prorationTitle}>Apply immediately</Text>
                  <Text style={styles.prorationDescription}>
                    Changes take effect now with prorated billing
                  </Text>
                </View>
                <View style={[
                  styles.radioButton,
                  prorationMode === 'immediate' && styles.selectedRadioButton,
                ]}>
                  {prorationMode === 'immediate' && (
                    <View style={styles.radioButtonInner} />
                  )}
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.prorationOption,
                  prorationMode === 'next_billing_cycle' && styles.selectedProrationOption,
                ]}
                onPress={() => setProrationMode('next_billing_cycle')}
              >
                <View style={styles.prorationContent}>
                  <Text style={styles.prorationTitle}>Next billing cycle</Text>
                  <Text style={styles.prorationDescription}>
                    Changes take effect on your next billing date
                  </Text>
                </View>
                <View style={[
                  styles.radioButton,
                  prorationMode === 'next_billing_cycle' && styles.selectedRadioButton,
                ]}>
                  {prorationMode === 'next_billing_cycle' && (
                    <View style={styles.radioButtonInner} />
                  )}
                </View>
              </TouchableOpacity>
            </View>
          )}
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={[styles.changeButton, !selectedPlan && styles.disabledButton]}
            onPress={handleChangePlan}
            disabled={!selectedPlan || isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFF" />
            ) : (
              <Text style={styles.changeButtonText}>
                {selectedPlan && isUpgrade(selectedPlan) ? 'Upgrade Plan' : 'Change Plan'}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#fff',
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  currentPlanSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  currentPlanCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  planBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  currentPlanInfo: {
    flex: 1,
  },
  currentPlanName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  currentPlanPrice: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  plansSection: {
    marginBottom: 24,
  },
  planCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedPlanCard: {
    borderColor: '#4CAF50',
  },
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  planInfo: {
    flex: 1,
    marginLeft: 12,
  },
  planName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  planPrice: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  planDescription: {
    fontSize: 14,
    color: '#666',
    marginLeft: 44,
  },
  upgradeTag: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  upgradeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
  },
  downgradeTag: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  downgradeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#fff',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 12,
    right: 12,
  },
  prorationSection: {
    marginBottom: 24,
  },
  prorationOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedProrationOption: {
    borderColor: '#2196F3',
  },
  prorationContent: {
    flex: 1,
  },
  prorationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  prorationDescription: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#ddd',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedRadioButton: {
    borderColor: '#2196F3',
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#2196F3',
  },
  footer: {
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  changeButton: {
    backgroundColor: '#2196F3',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  changeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});
