import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Product } from "react-native-iap";

// Define SubscriptionPlan interface locally since the old service is removed
interface SubscriptionPlan {
  id: string;
  productId: string;
  name: string;
  daysPerMonth: number;
  price: number;
  popular?: boolean;
  type: "subscription";
}

interface SubscriptionPlanCardProps {
  plan: SubscriptionPlan;
  isSelected: boolean;
  isCurrentPlan?: boolean;
  isUpgrade?: boolean;
  onSelect: (id: string) => void;
}

/**
 * Subscription plan card component
 * Displays a single subscription plan with price and selection state
 */
export const SubscriptionPlanCard: React.FC<SubscriptionPlanCardProps> = ({
  plan,
  isSelected,
  isCurrentPlan = false,
  isUpgrade = false,
  onSelect,
}) => {
  const price = plan.price === 0 ? "Free" : `$${plan.price}/month`;

  return (
    <TouchableOpacity
      style={[
        styles.planCard,
        isSelected && styles.selectedPlan,
        plan.popular && styles.popularPlan,
        isCurrentPlan && styles.currentPlan,
      ]}
      onPress={() => !isCurrentPlan && onSelect(plan.productId)}
      disabled={isCurrentPlan}
    >
      {plan.popular && (
        <View style={styles.popularBadge}>
          <Text style={styles.popularText}>Most Popular</Text>
        </View>
      )}

      {isCurrentPlan && (
        <View style={styles.currentPlanBadge}>
          <Text style={styles.currentPlanText}>Current Plan</Text>
        </View>
      )}

      {isUpgrade && (
        <View style={styles.upgradeBadge}>
          <Ionicons name="trending-up" size={12} color="#fff" />
          <Text style={styles.upgradeText}>Upgrade</Text>
        </View>
      )}



      <View style={styles.planHeader}>
        <Text style={styles.planName}>{plan.name}</Text>
        <View style={styles.priceContainer}>
          <Text style={styles.planPrice}>{price}</Text>
        </View>
      </View>

      <View style={styles.planDetails}>
        <View style={styles.daysAmount}>
          <Ionicons name="calendar" size={20} color="#2196F3" />
          <Text style={styles.daysText}>
            {plan.daysPerMonth} days per month
          </Text>
        </View>
        <View style={styles.feature}>
          <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
          <Text style={styles.featureText}>Automatic monthly renewal</Text>
        </View>
        <View style={styles.feature}>
          <Ionicons name="checkmark-circle" size={16} color="#4CAF50" />
          <Text style={styles.featureText}>Cancel anytime</Text>
        </View>
      </View>

      <View style={styles.selectionIndicator}>
        {isCurrentPlan ? (
          <Ionicons name="checkmark-circle" size={24} color="#2196F3" />
        ) : isSelected ? (
          <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
        ) : (
          <View style={styles.unselectedCircle} />
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  planCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.1)",
    position: "relative",
  },
  selectedPlan: {
    borderColor: "#2196F3",
    borderWidth: 2,
    shadowColor: "#2196F3",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  popularPlan: {
    borderColor: "#4CAF50",
    borderWidth: 2,
  },
  currentPlan: {
    borderColor: "#2196F3",
    borderWidth: 2,
    backgroundColor: "#F8F9FA",
  },
  unavailablePlan: {
    opacity: 0.6,
    backgroundColor: "#f5f5f5",
  },
  popularBadge: {
    position: "absolute",
    top: -10,
    right: 16,
    backgroundColor: "#4CAF50",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  popularText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
  currentPlanBadge: {
    position: "absolute",
    top: -10,
    left: 16,
    backgroundColor: "#2196F3",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  currentPlanText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
  planHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  planName: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  planPrice: {
    fontSize: 20,
    fontWeight: "700",
    color: "#333",
  },
  unavailableText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#999",
    fontStyle: "italic",
  },
  planDetails: {
    marginBottom: 8,
  },
  daysAmount: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  daysText: {
    fontSize: 16,
    color: "#666",
    marginLeft: 8,
    fontWeight: "500",
  },
  feature: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  featureText: {
    fontSize: 14,
    color: "#666",
    marginLeft: 8,
  },
  selectionIndicator: {
    position: "absolute",
    bottom: 16,
    right: 16,
  },
  unselectedCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: "#ccc",
  },
  upgradeBadge: {
    position: "absolute",
    top: -10,
    left: 12,
    backgroundColor: "#4CAF50",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1,
  },
  upgradeText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "600",
    marginLeft: 4,
  },

});
