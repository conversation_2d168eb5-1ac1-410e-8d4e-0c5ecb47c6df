/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '../../config/config.service';
import {
  IAppleVerificationReceiptResponse,
  IPaymentService,
  IReceiptValidationResult,
} from '../interfaces/payment-service.interface';
import { PaymentReceipt } from '../schemas/payment-receipt.schema';

@Injectable()
export class ApplePaymentService implements IPaymentService {
  private readonly logger = new Logger(ApplePaymentService.name);
  private readonly sandboxUrl =
    'https://sandbox.itunes.apple.com/verifyReceipt';
  private readonly productionUrl = 'https://buy.itunes.apple.com/verifyReceipt';
  private readonly sharedSecret: string;

  constructor(
    private readonly configService: ConfigService,
    @InjectModel(PaymentReceipt.name)
    private paymentReceiptModel: Model<PaymentReceipt>,
  ) {
    this.sharedSecret =
      this.configService.getPaymentConfig().appleAppSharedSecret;
  }

  async verifyReceiptLocally(receipt: string, productId: string, userId: string, metadata?: Record<string, any>): Promise<IAppleVerificationReceiptResponse> {
    return Promise.resolve<IAppleVerificationReceiptResponse>({
      status: 0,
      environment: 'Sandbox',
      receipt: {
        in_app: [
          {
            transaction_id: 'mock_transaction_id',
            original_transaction_id: 'mock_original_transaction_id',
            product_id: productId,
            purchase_date_ms: Date.now().toString(),
            expires_date_ms: Date.now().toString(),
          },
        ],
      },
      latest_receipt_info: [
        {
          transaction_id: 'mock_transaction_id',
          original_transaction_id: 'mock_original_transaction_id',
          product_id: productId,
          purchase_date_ms: Date.now().toString(),
          expires_date_ms: Date.now().toString(),
        },
      ],
    });
  }
  /**
   * Validate an Apple App Store receipt
   * @param receipt Receipt data from the App Store
   * @param productId Product ID from the App Store
   * @param userId User ID
   * @param metadata Additional metadata
   * @returns Validation result
   */
  async validateReceipt(
    receipt: string,
    productId: string,
    userId: string,
    metadata?: Record<string, any>,
  ): Promise<IReceiptValidationResult> {
    try {
      this.logger.log(
        `Validating Apple receipt for product ${productId} and user ${userId}`,
      );

      debugger;

      // First try production environment
      let validationResponse = await this.verifyWithApple(
        receipt,
        this.productionUrl,
      );

      // If we get a 21007 status, it's a sandbox receipt, so try the sandbox environment
      if (validationResponse.status === 21007) {
        this.logger.log('Receipt is from sandbox, retrying with sandbox URL');
        validationResponse = await this.verifyWithApple(
          receipt,
          this.sandboxUrl,
        );
      }

      if (this.configService.get('app.nodeEnv') === 'development' || this.configService.get('app.nodeEnv') === 'test') {
        validationResponse = await this.verifyReceiptLocally(
          receipt,
          productId,
          userId,
          metadata,
        );
      }

      // Check if the validation was successful
      if (validationResponse.status) {
        return {
          isValid: false,
          transactionId: '',
          productId,
          error: `Apple validation failed with status ${validationResponse.status}`,
          validationResponse,
        };
      }

      // Extract the purchase information
      const latestReceipt =
        validationResponse.latest_receipt_info?.[0] ||
        validationResponse.receipt?.in_app?.[0];

      if (!latestReceipt) {
        return {
          isValid: false,
          transactionId: '',
          productId,
          error: 'No purchase information found in receipt',
          validationResponse,
        };
      }

      // Extract transaction details
      const transactionId = latestReceipt.transaction_id;
      const originalTransactionId = latestReceipt.original_transaction_id;
      const purchaseDate = new Date(parseInt(latestReceipt.purchase_date_ms));
      const expiresDate = latestReceipt.expires_date_ms
        ? new Date(parseInt(latestReceipt.expires_date_ms))
        : undefined;

      return {
        isValid: true,
        transactionId,
        originalTransactionId,
        productId: latestReceipt.product_id,
        purchaseDate,
        expiryDate: expiresDate,
        validationResponse,
      };
    } catch (error) {
      this.logger.error(
        `Error validating Apple receipt: ${error.message}`,
        error.stack,
      );
      return {
        isValid: false,
        transactionId: '',
        productId,
        error: `Error validating receipt: ${error.message}`,
      };
    }
  }

  /**
   * Check if a transaction has already been processed
   * @param transactionId Transaction ID to check
   * @returns True if the transaction has been processed
   */
  async isTransactionProcessed(transactionId: string): Promise<boolean> {
    const existingReceipt = await this.paymentReceiptModel.findOne({
      transactionId,
    });
    return !!existingReceipt;
  }

  /**
   * Verify a receipt with the Apple App Store
   * @param receipt Receipt data
   * @param url Validation URL (production or sandbox)
   * @returns Validation response from Apple
   */
  private async verifyWithApple(receipt: string, url: string): Promise<any> {
    const requestBody = {
      'receipt-data': receipt,
      password: this.sharedSecret,
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error(
        `Apple verification failed with status ${response.status}`,
      );
    }

    return await response.json();
  }
}
