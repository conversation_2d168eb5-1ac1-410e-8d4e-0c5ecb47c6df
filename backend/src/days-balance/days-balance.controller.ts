import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  Request,
  UseGuards,
  BadRequestException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { DaysBalanceService } from './days-balance.service';
import { DayPackPurchaseDto, SUBSCRIPTION_PLANS } from './dto/subscription-plan.dto';
import { DayBalanceResponseDto, PaginatedDayTransactionResponseDto } from './dto/day-balance-response.dto';

@Controller('balance')
@UseGuards(AuthGuard('jwt'))
export class DaysBalanceController {
  private readonly logger = new Logger(DaysBalanceController.name);

  constructor(private readonly daysBalanceService: DaysBalanceService) { }

  /**
   * Get current user's day balance
   */
  @Get()
  async getBalance(@Request() req: any): Promise<DayBalanceResponseDto> {
    try {
      if (!req.user?.userId) {
        throw new BadRequestException('User authentication required');
      }

      const balance = await this.daysBalanceService.getBalance(req.user.userId);

      if (!balance) {
        // Initialize with basic plan if no balance exists
        this.logger.log(`Initializing balance for new user: ${req.user.userId}`);
        const newBalance = await this.daysBalanceService.initializeUserBalance(req.user.userId);
        return {
          subscriptionDays: newBalance.subscriptionDays,
          packDays: newBalance.packDays,
          totalDays: newBalance.subscriptionDays + newBalance.packDays,
          totalDaysUsed: newBalance.totalDaysUsed,
          totalDaysAdded: newBalance.totalDaysAdded,
          currentPlan: newBalance.currentPlan,
          subscriptionStatus: newBalance.subscriptionStatus,
          subscriptionStartDate: newBalance.subscriptionStartDate,
          subscriptionEndDate: newBalance.subscriptionEndDate,
          nextBillingDate: newBalance.nextBillingDate,
          lastResetDate: newBalance.lastResetDate,
        };
      }

      return {
        subscriptionDays: balance.subscriptionDays,
        packDays: balance.packDays,
        totalDays: balance.subscriptionDays + balance.packDays,
        totalDaysUsed: balance.totalDaysUsed,
        totalDaysAdded: balance.totalDaysAdded,
        currentPlan: balance.currentPlan,
        subscriptionStatus: balance.subscriptionStatus,
        subscriptionStartDate: balance.subscriptionStartDate,
        subscriptionEndDate: balance.subscriptionEndDate,
        nextBillingDate: balance.nextBillingDate,
        lastResetDate: balance.lastResetDate,
        pendingPlanChange: balance.pendingPlanChange,
      };
    } catch (error) {
      this.logger.error(`Error getting balance for user: ${req.user?.userId}`, error.stack);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get user balance');
    }
  }

  /**
   * Get transaction history
   */
  @Get('transactions')
  async getTransactionHistory(
    @Request() req: any,
    @Query('limit') limit: string = '10',
    @Query('page') page: string = '1',
  ): Promise<PaginatedDayTransactionResponseDto> {
    const limitNum = parseInt(limit, 10);
    const pageNum = parseInt(page, 10);

    if (limitNum <= 0 || limitNum > 100) {
      throw new BadRequestException('Limit must be between 1 and 100');
    }

    if (pageNum <= 0) {
      throw new BadRequestException('Page must be greater than 0');
    }

    const result = await this.daysBalanceService.getTransactionHistory(
      req.user.userId,
      limitNum,
      pageNum,
    );

    return {
      data: result.data.map(transaction => ({
        id: (transaction as any)._id.toString(),
        userId: transaction.userId,
        type: transaction.type,
        amount: transaction.amount,
        balanceAfter: transaction.balanceAfter,
        source: transaction.source,
        tripId: transaction.tripId,
        description: transaction.description,
        metadata: transaction.metadata,
        expiresAt: transaction.expiresAt,
        createdAt: (transaction as any).createdAt,
        updatedAt: (transaction as any).updatedAt,
      })),
      pagination: result.pagination,
    };
  }

  /**
   * Purchase day packs
   */
  @Post('purchase-pack')
  async purchaseDayPack(
    @Request() req: any,
    @Body() purchaseDto: DayPackPurchaseDto,
  ): Promise<DayBalanceResponseDto> {
    if (purchaseDto.quantity <= 0 || purchaseDto.quantity > 10) {
      throw new BadRequestException('Quantity must be between 1 and 10');
    }

    const updatedBalance = await this.daysBalanceService.purchaseDayPack(
      req.user.userId,
      purchaseDto.quantity,
      { paymentMethodId: purchaseDto.paymentMethodId },
    );

    return {
      subscriptionDays: updatedBalance.subscriptionDays,
      packDays: updatedBalance.packDays,
      totalDays: updatedBalance.subscriptionDays + updatedBalance.packDays,
      totalDaysUsed: updatedBalance.totalDaysUsed,
      totalDaysAdded: updatedBalance.totalDaysAdded,
      currentPlan: updatedBalance.currentPlan,
      subscriptionStatus: updatedBalance.subscriptionStatus,
      subscriptionStartDate: updatedBalance.subscriptionStartDate,
      subscriptionEndDate: updatedBalance.subscriptionEndDate,
      nextBillingDate: updatedBalance.nextBillingDate,
      lastResetDate: updatedBalance.lastResetDate,
      pendingPlanChange: updatedBalance.pendingPlanChange,
    };
  }

  /**
   * Get available subscription plans
   */
  @Get('plans')
  async getSubscriptionPlans() {
    return Object.values(SUBSCRIPTION_PLANS);
  }

  /**
   * Check if user has sufficient days for trip generation
   */
  @Get('check-sufficient')
  async checkSufficientDays(
    @Request() req: any,
    @Query('days') days: string = '1',
  ): Promise<{ sufficient: boolean; available: number; required: number }> {
    const daysRequired = parseInt(days, 10);

    if (daysRequired <= 0) {
      throw new BadRequestException('Days must be greater than 0');
    }

    const available = await this.daysBalanceService.getTotalAvailableDays(req.user.userId);
    const sufficient = await this.daysBalanceService.hasSufficientDays(req.user.userId, daysRequired);

    return {
      sufficient,
      available,
      required: daysRequired,
    };
  }
}
