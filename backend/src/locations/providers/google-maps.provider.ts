/* eslint-disable */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from 'src/config/config.service';
import {
  LocationContext,
  LocationCoordinates,
} from '../interfaces/location-coordinates.interface';
import { BaseMapsProvider } from './base-maps.provider';

@Injectable()
export class GoogleMapsService extends BaseMapsProvider {
  constructor(private readonly configService: ConfigService) {
    super();
    this.logger = new Logger(GoogleMapsService.name);
    this.apiKey = this.configService.getGoogleMapsApiKey();
    this.baseUrl = 'https://maps.googleapis.com/maps/api/geocode/json';
    if (!this.apiKey || !this.baseUrl) {
      this.logger.warn(
        'Google Maps API key or API URL is not set. Google Maps geocoding will not work.',
      );
    }
  }

  override async geocode(
    location: string,
    context?: LocationContext,
  ): Promise<LocationCoordinates | null> {
    try {
      // Build the search query with context
      let searchQuery = location;

      // Add city and country to the search query if provided, but avoid duplicating city name
      if (context?.city && context?.country) {
        // Only add city if it's not already the same as the location being searched
        if (location.toLowerCase() !== context.city.toLowerCase()) {
          searchQuery = `${location}, ${context.city}, ${context.country}`;
        } else {
          searchQuery = `${location}, ${context.country}`;
        }
      } else if (context?.city) {
        // Only add city if it's not already the same as the location being searched
        if (location.toLowerCase() !== context.city.toLowerCase()) {
          searchQuery = `${location}, ${context.city}`;
        } else {
          searchQuery = location;
        }
      } else if (context?.country) {
        searchQuery = `${location}, ${context.country}`;
      }

      this.logger.log(`Geocoding with context: ${searchQuery}`);

      // Use geocoding API instead of place search
      let url = `${this.baseUrl}?address=${encodeURIComponent(searchQuery)}&key=${this.apiKey}`;

      // Add country restriction if country code is provided
      if (context?.countryCode) {
        url += `&components=country:${context.countryCode}`;
      }

      const response = await fetch(url);
      const data = await response.json();

      debugger;

      if (data.status !== 'OK' || !data.results || data.results.length === 0) {
        debugger;
        this.logger.debug(`No geocoding results found for: ${searchQuery}`);
        return null;
      }



      const result = data.results[0];

      // Verify coordinates are within expected bounds
      const coordinates = {
        lat: result.geometry.location.lat,
        lng: result.geometry.location.lng,
      };

      if (!this.areCoordinatesValid(coordinates.lat, coordinates.lng)) {
        this.logger.warn(
          `Invalid coordinates from geocoding: ${coordinates.lat}, ${coordinates.lng}`,
        );
        return null;
      }

      return {
        name: location, // Use the original location name for precise validation
        formatted_address: result.formatted_address,
        coordinates,
        place_id: result.place_id,
        types: result.types,
        source: 'google_maps',
        confidence: 'medium', // Lower confidence since this is a fallback
      };
    } catch (error) {
      debugger;
      this.logger.error(`Error geocoding with context: ${error.message}`);
      return null;
    }
  }
}
